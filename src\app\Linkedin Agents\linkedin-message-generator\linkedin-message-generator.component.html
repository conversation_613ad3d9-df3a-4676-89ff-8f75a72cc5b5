<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>💬 LinkedIn Message Generator</h2>
    <!-- <button class="btn btn-primary" (click)="createNewMessage()">➕ Create Message</button> -->
    <div class="d-flex gap-2">
    <button class="btn btn-primary" (click)="handleHeaderAction(1)">➕ Create Message</button>
    <button class="btn btn-primary" (click)="handleHeaderAction(2)">➕ LinkedIn Sales Navigator</button>
  </div>
  </div>

  <div class="table-responsive" *ngIf="users.length > 0; else noData">
    <table class="table">
      <thead>
        <tr>
          <th>User Name</th>
          <th>Message 1</th>
          <th>Message 2</th>
          <th>Message 3</th>
          <th>Message 4</th>
          <th>Message 5</th>
          <th style="text-align: center;">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let user of users">
          <td>
            <a [href]="user.profileUrl" target="_blank">{{ user.profileName }}</a>
          </td>
          <td>{{ user.message1 }}</td>
          <td>{{ user.message2 }}</td>
          <td>{{ user.message3 }}</td>
          <td>{{ user.message4 }}</td>
          <td>{{ user.message5 }}</td>
          <td>
            <div class="action-buttons">
              <button class="btn-view" (click)="viewUser(user.id)">View</button>
              <button class="btn-delete" (click)="deleteUser(user)">Delete</button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <ng-template #noData>
    <div class="alert alert-info">No users found. Please create a message.</div>
  </ng-template>
</div>
