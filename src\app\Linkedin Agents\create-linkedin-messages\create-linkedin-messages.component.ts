import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Case<PERSON>ipe } from '@angular/common';
import { Component } from '@angular/core';
import { ApisService } from '../../Services/apis.service';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-create-linkedin-messages',
  standalone: true,
  imports: [NgFor, NgIf, FormsModule,TitleCasePipe],
  templateUrl: './create-linkedin-messages.component.html',
  styleUrl: './create-linkedin-messages.component.scss'
})
export class CreateLinkedinMessagesComponent {
  url = '';
  isLoading = false;
  result: any = null;
  error: string | null = null;
  messageKeyList: string[] = [];
  mode = 1;

  linkedinProfilePattern = /^https:\/\/(www\.)?linkedin\.com\/in\/[A-Za-z0-9\-_%]+\/?$/;
  salesNavigatorPattern = /^https:\/\/(www\.)?linkedin\.com\/sales\/profile\/[A-Za-z0-9\-_%]+\/?$/;

  constructor(private agentService: ApisService) {}

  objectKeys = Object.keys;

  fetchProfile() {
    if (!this.isValidUrl()) {
      this.error = this.getUrlErrorMessage();
      return;
    }

    this.isLoading = true;
    this.result = null;
    this.error = null;

    // Call different API methods based on URL type
    const apiCall = this.isValidLinkedinUrl()
      ? this.agentService.GeneratorLinkedinMessage(this.url)
      : this.agentService.GeneratorSalesNavigatorMessage(this.url);

    apiCall.subscribe({
      next: (response) => {
        this.result = response.messages;
        console.log("Response Testing", this.result)
        this.isLoading = false;
      },
      error: (err) => {
        this.error = err.message || 'Something went wrong.';
        this.isLoading = false;
      }
    });
  }

  isValidUrl(): boolean {
    // Auto-detect URL type and set mode
    if (this.isValidLinkedinUrl()) {
      this.mode = 1;
      return true;
    } else if (this.isValidSalesNavigatorUrl()) {
      this.mode = 2;
      return true;
    }
    return false;
  }

  isValidLinkedinUrl(): boolean {
    return this.linkedinProfilePattern.test(this.url.trim());
  }

  isValidSalesNavigatorUrl(): boolean {
    return this.salesNavigatorPattern.test(this.url.trim());
  }

  getUrlErrorMessage(): string {
    if (this.mode === 1) {
      return 'Please enter a valid LinkedIn profile URL.';
    } else if (this.mode === 2) {
      return 'Please enter a valid LinkedIn Sales Navigator URL.';
    }
    return 'Please enter a valid URL.';
  }

 messageKeys(): string[] {
  return Object.keys(this.result || {}).filter(key => key.startsWith('message'));
  }

  hasGeneratedMessages(): boolean {
  return this.messageKeys().some(key => this.result?.[key]);
  }

  approveMessage() {
  console.log('Message approved:', this.result);
  }

   ScrapeProfileAgain() {
  }


}
