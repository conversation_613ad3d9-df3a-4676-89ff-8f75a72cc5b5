import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import * as pdfjsLib from 'pdfjs-dist';


@Component({
  selector: 'app-resume-screening-agent',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './resume-screening-agent.component.html',
  styleUrl: './resume-screening-agent.component.scss'
})
export class ResumeScreeningAgentComponent {

  jobDescriptionControl = new FormControl('');
  extractedResumeText = '';
  matchResult = '';

  constructor() {
    (pdfjsLib as any).GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/5.3.31/pdf.worker.min.mjs`;
  }

  async onFileSelected(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file || file.type !== 'application/pdf') {
      alert('Please upload a valid PDF file.');
      return;
    }

    this.extractedResumeText = await this.extractTextFromPDF(file);
  }

  async extractTextFromPDF(file: File): Promise<string> {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    let text = '';

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const content = await page.getTextContent();
      const pageText = content.items.map((item: any) => item.str).join(' ');
      text += pageText + '\n';
    }

    return text;
  }

  screenResume() {
    const jobDesc = this.jobDescriptionControl.value || '';
    const resume = this.extractedResumeText || '';

    if (jobDesc && resume) {
      const jdWords = jobDesc.toLowerCase().split(/\W+/);
      const resumeWords = resume.toLowerCase().split(/\W+/);
      const matchCount = jdWords.filter(word => resumeWords.includes(word)).length;
      const matchPercentage = Math.round((matchCount / jdWords.length) * 100);

      this.matchResult = `📊 Match Score: ${matchPercentage}%`;
    } else {
      this.matchResult = '⚠️ Please enter a job description and upload a resume.';
    }
  }
}
